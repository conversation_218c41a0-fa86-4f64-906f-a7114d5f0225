# 图像边界坐标显示优化说明

## 问题描述

之前的实现存在一个重要问题：当光标移出图像显示区域范围时，坐标信息仍然在变换和显示。这与 ImageJ 等专业图像处理软件的行为不一致。

### 问题表现
- ✅ **RGB 值逻辑正确**：移出图像范围时不显示 RGB 值
- ❌ **坐标显示错误**：移出图像范围时仍然显示坐标信息
- ❌ **用户体验不佳**：显示无意义的坐标信息

### 期望行为
仿照 ImageJ 的逻辑：
- **图像内**：显示 `x=1024, y=768, value=255,128,064`
- **图像外**：不显示任何信息（空白状态栏）

## 根本原因分析

### 1. 事件触发机制
在 `RenderView.cs` 的 `RaisePixelInfo` 方法中：

```csharp
bool inBounds = px >= 0 && py >= 0 && px < Source.PixelSize.Width && py < Source.PixelSize.Height;
if (!inBounds)
{
    _lastPixel = new LastPixel { X = px, Y = py, R = 0, G = 0, B = 0, InBounds = false };
    PixelInfoChanged?.Invoke(this, new PixelInfoEventArgs(px, py, 0, 0, 0, false));
    // 仍然触发事件并传递坐标信息！
}
```

**问题**：即使 `InBounds = false`，仍然传递坐标信息并触发事件。

### 2. 状态显示逻辑
在 `ImageView.axaml.cs` 的 `UpdateDefaultStatusText` 方法中：

```csharp
if (_lastPixelInfo.InBounds)
{
    StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}, value={_lastPixelInfo.R:000},{_lastPixelInfo.G:000},{_lastPixelInfo.B:000}";
}
else
{
    StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}";  // 问题：仍然显示坐标！
}
```

**问题**：在 `else` 分支中仍然显示坐标信息。

## 解决方案

### 修改状态显示逻辑

将 `UpdateDefaultStatusText` 方法中的 `else` 分支修改为完全不显示信息：

```csharp
// 更新默认状态的状态文本（坐标和RGB值）
private void UpdateDefaultStatusText()
{
    if (StatusText == null || _lastPixelInfo == null) return;

    if (_lastPixelInfo.InBounds)
    {
        StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}, value={_lastPixelInfo.R:000},{_lastPixelInfo.G:000},{_lastPixelInfo.B:000}";
    }
    else
    {
        // 光标移出图像范围时，不显示任何坐标信息，仿照ImageJ的行为
        StatusText.Text = "";
    }
}
```

### 优化效果

#### 修改前
- **图像内**：`x=1024, y=768, value=255,128,064`
- **图像外**：`x=-50, y=-30` ❌ 显示无意义坐标

#### 修改后
- **图像内**：`x=1024, y=768, value=255,128,064`
- **图像外**：`` ✅ 空白，不显示任何信息

## 技术实现细节

### 1. 边界检查逻辑
边界检查在 `RaisePixelInfo` 方法中进行：

```csharp
// 将DIP坐标映射到实际像素（考虑位图DPI缩放）
double scaleX = Source.PixelSize.Width / Source.Size.Width;
double scaleY = Source.PixelSize.Height / Source.Size.Height;
int px = (int)Math.Floor(imgPos.X * scaleX);
int py = (int)Math.Floor(imgPos.Y * scaleY);

bool inBounds = px >= 0 && py >= 0 && px < Source.PixelSize.Width && py < Source.PixelSize.Height;
```

### 2. 状态优先级保持
修改不影响其他状态的显示优先级：

1. **矩形绘制状态**：`x=100, y=50, w=200, h=150, θ=0.0°`
2. **直线绘制状态**：`length=223.6, θ=26.6°`
3. **选中的 RenderView 矩形**：`x=100, y=50, w=200, h=150, θ=45.0°`
4. **选中的 ROI 旋转矩形**：`RotRect C=(100,50) θ=45°`
5. **默认状态（图像内）**：`x=1024, y=768, value=255,128,064`
6. **默认状态（图像外）**：`` （空白）

### 3. 事件处理保持不变
`OnRenderViewPixelInfoChanged` 方法保持不变，确保事件处理逻辑的一致性：

```csharp
private void OnRenderViewPixelInfoChanged(object? sender, PixelInfoEventArgs e)
{
    _lastPixelInfo = e;
    // 如果当前不在绘制状态且没有选中形状，立即更新状态文本
    if (_renderView != null && !_renderView.IsDrawingRect && !_renderView.IsDrawingLine && _renderView.SelectedShape == null)
    {
        UpdateDefaultStatusText();
    }
}
```

## 用户体验改进

### 1. ImageJ 兼容性
完全符合 ImageJ 的行为模式：
- 图像内：显示完整的坐标和像素值信息
- 图像外：状态栏保持空白，不显示无意义信息

### 2. 视觉清晰度
- **减少干扰**：移除无意义的坐标显示
- **状态明确**：空白状态栏明确表示光标不在图像区域
- **专业体验**：与专业图像处理软件的行为一致

### 3. 信息准确性
- **有效信息**：只显示有意义的坐标和像素值
- **边界明确**：清楚区分图像内外的状态
- **无误导性**：避免显示图像外的无效坐标

## 测试场景

### 1. 基本边界测试
1. 在图像内移动鼠标
   - **验证**：显示 `x=1024, y=768, value=255,128,064`
2. 移动鼠标到图像边界外
   - **验证**：状态栏变为空白
3. 重新移回图像内
   - **验证**：立即恢复坐标和 RGB 显示

### 2. 绘制状态测试
1. 在矩形绘制状态下移出图像
   - **验证**：仍然显示矩形绘制信息，不受边界影响
2. 完成绘制后移出图像
   - **验证**：状态栏变为空白

### 3. 选中状态测试
1. 选中矩形后移出图像
   - **验证**：仍然显示选中矩形信息，不受边界影响
2. 取消选中后移出图像
   - **验证**：状态栏变为空白

### 4. 快速移动测试
1. 快速在图像内外移动鼠标
   - **验证**：状态显示能够快速响应边界变化
2. 验证性能表现
   - **验证**：无延迟，无闪烁

## 修改的文件

### 主要文件
**AvaloniaApplication2/Views/ImageView.axaml.cs**
- 修改 `UpdateDefaultStatusText()` 方法
- 优化图像边界外的状态显示逻辑

### 修改内容
```csharp
// 修改前
else
{
    StatusText.Text = $"x={_lastPixelInfo.X}, y={_lastPixelInfo.Y}";
}

// 修改后
else
{
    // 光标移出图像范围时，不显示任何坐标信息，仿照ImageJ的行为
    StatusText.Text = "";
}
```

## 优势

1. **ImageJ 兼容**：完全符合 ImageJ 的显示行为
2. **用户体验**：减少无意义信息的干扰
3. **专业性**：与专业图像处理软件保持一致
4. **清晰度**：状态信息更加准确和有意义
5. **性能**：不影响现有的性能表现
6. **兼容性**：不影响其他功能的正常工作

## 注意事项

1. **状态优先级**：绘制状态和选中状态的显示不受图像边界影响
2. **事件机制**：底层的像素信息事件机制保持不变
3. **性能影响**：修改对性能无负面影响
4. **向后兼容**：不影响现有功能的正常使用

现在的实现完全符合 ImageJ 的行为：只在图像显示区域范围内显示坐标信息，移出图像范围时状态栏保持空白！
