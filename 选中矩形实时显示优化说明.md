# 选中矩形实时显示优化说明

## 优化目标

进一步优化实时显示逻辑，实现以下功能：
1. **移除现有的 RECT(0,0) 显示内容**：当选中绘制矩形框时
2. **实时显示选中矩形的信息**：左上角坐标、宽度、高度和旋转角度θ
3. **智能状态切换**：根据当前状态自动切换显示内容

## 实现方案

### 1. 添加形状选择状态监听

#### 新增事件机制
在 `RenderView.cs` 中添加了形状选择状态变化事件：

```csharp
// 形状选择状态变化事件：当形状的选择状态发生变化时触发
public event EventHandler? ShapeSelectionChanged;
```

#### 智能事件触发
- 修改了 `ClearSelection()` 方法，在清除选择时触发事件
- 添加了 `SetShapeSelected()` 方法，在设置选择状态时触发事件
- 确保只在状态真正发生变化时才触发事件

### 2. 优化状态显示优先级

#### 新的显示优先级（按顺序）：
1. **矩形绘制状态**：`x=100, y=50, w=200, h=150, θ=0.0°`
2. **直线绘制状态**：`length=223.6, θ=26.6°`
3. **选中的 RenderView 矩形**：`x=100, y=50, w=200, h=150, θ=0.0°`
4. **选中的 ROI 旋转矩形**：`RotRect C=(100,50) θ=45°`
5. **默认状态**：`x=1024, y=768, value=255,128,064`

#### 移除的显示内容：
- ❌ `Rect TL=(0,0) θ=0°` - 这种显示内容已被移除

### 3. 实时选中矩形信息显示

#### 显示逻辑
```csharp
// 检查是否有选中的RenderView形状（优先显示）
var selectedShape = _renderView.SelectedShape;
if (selectedShape is RectShape selectedRect && _renderView.Source != null)
{
    // 显示选中矩形的实时信息：左上角坐标、宽度、高度和旋转角度
    var source = _renderView.Source;
    double scaleX = source.PixelSize.Width / source.Size.Width;
    double scaleY = source.PixelSize.Height / source.Size.Height;

    var bounds = selectedRect.Rect;
    int x = (int)Math.Floor(bounds.Left * scaleX);
    int y = (int)Math.Floor(bounds.Top * scaleY);
    int w = (int)Math.Round(bounds.Width * scaleX);
    int h = (int)Math.Round(bounds.Height * scaleY);
    double angle = selectedRect.AngleDeg;

    StatusText.Text = $"x={x}, y={y}, w={w}, h={h}, θ={angle:F1}°";
}
```

#### 特性
- **实时更新**：选中状态变化时立即更新显示
- **精确计算**：正确的坐标转换和角度显示
- **ImageJ 兼容**：显示格式与 ImageJ 完全一致

## 技术实现细节

### 1. 事件驱动的状态更新

#### ShapeSelectionChanged 事件处理器
```csharp
private void OnRenderViewShapeSelectionChanged(object? sender, EventArgs e)
{
    // 当形状选择状态发生变化时，立即更新状态显示
    if (_renderView != null && DataContext is ImageViewModel vm)
    {
        var selectedShape = _renderView.SelectedShape;
        if (selectedShape is RectShape selectedRect && _renderView.Source != null)
        {
            // 显示选中矩形的信息
            // ... 计算和显示逻辑
        }
        else
        {
            // 没有选中形状，显示默认信息
            UpdateDefaultStatusText();
        }
    }
}
```

### 2. 智能状态管理

#### 形状选择状态设置
```csharp
private void SetShapeSelected(IShape shape, bool selected)
{
    bool wasSelected = shape.Selected;
    shape.Selected = selected;
    if (wasSelected != selected)
    {
        ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
    }
}
```

#### 选择清除优化
```csharp
private void ClearSelection()
{
    bool hadSelection = Shapes.Any(s => s.Selected);
    foreach (var s in Shapes) s.Selected = false;
    if (hadSelection)
    {
        ShapeSelectionChanged?.Invoke(this, EventArgs.Empty);
    }
}
```

### 3. 类型系统优化

#### 接口统一
- 将 `SelectedShape` 属性类型从 `ShapeBase?` 改为 `IShape?`
- 将 `SetShapeSelected` 方法参数类型从 `ShapeBase` 改为 `IShape`
- 确保与现有的形状系统完全兼容

## 修改的文件

### 主要文件
1. **AvaloniaApplication2/ImageViewer/Controls/RenderView.cs**
   - 添加 `ShapeSelectionChanged` 事件
   - 添加 `SelectedShape` 属性
   - 修改 `ClearSelection()` 方法
   - 添加 `SetShapeSelected()` 方法
   - 更新多个选择设置点

2. **AvaloniaApplication2/Views/ImageView.axaml.cs**
   - 添加 `ShapeSelectionChanged` 事件绑定
   - 添加 `OnRenderViewShapeSelectionChanged` 事件处理器
   - 优化 `UpdateStatusText` 方法的显示优先级
   - 移除 `RoiRectangle` 的显示逻辑

## 使用效果

### 1. 矩形绘制时
- **绘制过程中**：`x=100, y=50, w=200, h=150, θ=0.0°`
- **完成绘制后**：如果矩形被选中，继续显示相同格式的信息

### 2. 矩形选中时
- **选中矩形**：`x=100, y=50, w=200, h=150, θ=45.0°`
- **取消选中**：立即切换回默认的坐标和 RGB 显示

### 3. 状态切换
- **选中 → 取消选中**：立即切换显示内容
- **不同形状间切换**：正确显示对应形状的信息
- **绘制 → 选中**：无缝切换，保持信息连续性

## 测试场景

### 1. 矩形绘制和选中测试
1. 选择矩形工具并绘制矩形
2. 验证绘制过程中的实时信息显示
3. 完成绘制后，验证选中状态的信息显示
4. 点击空白区域取消选中，验证切换回默认显示

### 2. 多矩形选择测试
1. 绘制多个矩形
2. 依次选中不同的矩形
3. 验证每次选中时显示对应矩形的信息
4. 验证角度信息的正确显示（包括旋转后的矩形）

### 3. 工具切换测试
1. 在矩形选中状态下切换到其他工具
2. 验证状态显示的正确切换
3. 切换回编辑模式，验证选中状态的恢复

### 4. 边界情况测试
1. 快速选中和取消选中
2. 在绘制过程中切换选择
3. 验证事件触发的正确性和性能

## 优势

1. **用户体验优化**：移除了无用的 RECT(0,0) 显示，提供更有意义的信息
2. **实时响应**：选中状态变化时立即更新显示，无延迟
3. **信息连续性**：绘制完成后选中状态的信息显示保持一致
4. **ImageJ 兼容**：完全符合 ImageJ 的显示格式和逻辑
5. **性能优化**：只在状态真正变化时才触发事件和更新
6. **架构清晰**：基于事件驱动的设计，易于维护和扩展

## 注意事项

1. **事件频率**：确保事件只在必要时触发，避免性能问题
2. **状态同步**：确保显示状态与实际选择状态保持同步
3. **类型安全**：正确处理 IShape 到具体形状类型的转换
4. **内存管理**：正确绑定和解绑事件，避免内存泄漏

现在的实现完全符合你的要求：移除了 RECT(0,0) 显示，实现了选中矩形的实时信息显示，并且与 ImageJ 的显示逻辑完全一致！
