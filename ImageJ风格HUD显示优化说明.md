# ImageJ 风格 HUD 显示优化说明

## 功能概述

按照 ImageJ 的显示逻辑，优化了 RenderView 控件左下角的状态信息显示，实现了根据当前绘制状态动态切换显示内容的功能。

## 显示逻辑

### 1. 默认状态（无绘制操作）
**显示内容**：当前鼠标坐标位置以及 RGB 三色值
**格式**：`x=4050, y=1194, RGB=(255,128,64)`

### 2. 矩形绘制状态
**显示内容**：矩形框左上角坐标、宽度、高度和旋转角度
**格式**：`x=4050, y=1194, w=612, h=384, θ=0.0°`

**说明**：
- `x`, `y`：矩形左上角的像素坐标
- `w`, `h`：矩形的宽度和高度（像素单位）
- `θ`：旋转角度（度数，保留1位小数）

### 3. 直线绘制状态
**显示内容**：线条长度和角度
**格式**：`length=450.2, θ=45.0°`

**说明**：
- `length`：线条长度（像素单位，保留1位小数）
- `θ`：线条角度（相对于水平线，逆时针为正，0-360度范围）

### 4. 其他绘制状态
对于多边形、圆形、自由绘制等其他绘制工具，当前显示默认的鼠标坐标和 RGB 值。
后续可根据需要扩展特定的显示逻辑。

## 技术实现

### 1. 新增 DrawHud 方法
在 `RenderView.cs` 中添加了专门的 HUD 绘制方法，负责在控件左下角绘制状态信息。

### 2. 智能文本生成
通过 `GetHudText()` 方法根据当前绘制状态智能生成显示文本：
- 检查当前是否在进行矩形绘制（`_isDrawingRect && _activeRect != null`）
- 检查当前是否在进行直线绘制（`_isDrawingLine && _activeLine != null`）
- 默认情况显示鼠标坐标和 RGB 值

### 3. 坐标系转换
正确处理图像坐标到像素坐标的转换：
```csharp
double scaleX = Source.PixelSize.Width / Source.Size.Width;
double scaleY = Source.PixelSize.Height / Source.Size.Height;
int pixelX = (int)Math.Floor(imageX * scaleX);
int pixelY = (int)Math.Floor(imageY * scaleY);
```

### 4. LineShape 扩展
为 `LineShape` 类添加了公共属性 `Start` 和 `End`，用于获取线条的起点和终点坐标。

## 代码修改

### 主要文件
1. **AvaloniaApplication2/ImageViewer/Controls/RenderView.cs**
   - 添加 `DrawHud()` 方法
   - 添加 `GetHudText()` 方法
   - 添加 `GetRectangleHudText()` 方法
   - 添加 `GetLineHudText()` 方法
   - 添加 `GetDefaultHudText()` 方法
   - 在 `Render()` 方法中调用 `DrawHud()`

2. **AvaloniaApplication2/ImageViewer/Drawing/Shapes/LineShape.cs**
   - 添加 `Start` 属性
   - 添加 `End` 属性

### 关键方法

#### GetRectangleHudText
```csharp
private string GetRectangleHudText(RectShape rect)
{
    // 获取矩形边界并转换为像素坐标
    var bounds = rect.Rect;
    int x = (int)Math.Floor(bounds.Left * scaleX);
    int y = (int)Math.Floor(bounds.Top * scaleY);
    int w = (int)Math.Round(bounds.Width * scaleX);
    int h = (int)Math.Round(bounds.Height * scaleY);
    double angle = rect.AngleDeg;
    
    return $"x={x}, y={y}, w={w}, h={h}, θ={angle:F1}°";
}
```

#### GetLineHudText
```csharp
private string GetLineHudText(LineShape line)
{
    // 计算线条长度和角度
    var start = line.Start;
    var end = line.End;
    var dx = end.X - start.X;
    var dy = end.Y - start.Y;
    
    double lengthPixels = Math.Sqrt((dx * scaleX)² + (dy * scaleY)²);
    double angleDeg = Math.Atan2(dy, dx) * 180.0 / Math.PI;
    if (angleDeg < 0) angleDeg += 360;
    
    return $"length={lengthPixels:F1}, θ={angleDeg:F1}°";
}
```

## 使用效果

### 矩形绘制
1. 选择矩形绘制工具
2. 开始拖拽绘制矩形
3. 左下角实时显示：`x=100, y=50, w=200, h=150, θ=0.0°`
4. 释放鼠标完成绘制后，切换回默认显示

### 直线绘制
1. 选择直线绘制工具
2. 开始拖拽绘制直线
3. 左下角实时显示：`length=223.6, θ=26.6°`
4. 释放鼠标完成绘制后，切换回默认显示

### 默认状态
1. 在任何非绘制状态下移动鼠标
2. 左下角实时显示：`x=1024, y=768, RGB=(128,255,64)`

## 特性

1. **实时更新**：绘制过程中信息实时更新
2. **自动切换**：根据绘制状态自动切换显示内容
3. **精确计算**：正确处理坐标转换和角度计算
4. **ImageJ 兼容**：显示格式与 ImageJ 保持一致
5. **性能优化**：只在需要时重新计算和绘制

## 后续扩展

可以根据需要为其他绘制工具添加专门的 HUD 显示逻辑：
- 圆形/椭圆：显示中心坐标、半径或长短轴
- 多边形：显示顶点数量、面积
- 自由绘制：显示路径长度
- 橡皮擦：显示擦除半径

## 注意事项

1. **坐标系统**：确保图像坐标正确转换为像素坐标
2. **角度计算**：线条角度采用数学标准（逆时针为正）
3. **精度控制**：长度和角度保留适当的小数位数
4. **性能考虑**：避免在高频更新中进行复杂计算
