is_global = true
build_property.AvaloniaNameGeneratorIsEnabled = true
build_property.AvaloniaNameGeneratorBehavior = InitializeComponent
build_property.AvaloniaNameGeneratorDefaultFieldModifier = internal
build_property.AvaloniaNameGeneratorFilterByPath = *
build_property.AvaloniaNameGeneratorFilterByNamespace = *
build_property.AvaloniaNameGeneratorViewFileNamingStrategy = NamespaceAndClassName
build_property.AvaloniaNameGeneratorAttachDevTools = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = AvaloniaApplication2
build_property.ProjectDir = E:\AvaloniaApplication9.9-version1\AvaloniaApplication2\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/App.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Styles.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/GalleryView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/ImageView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/InspectView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/MainWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/ProjectView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/SplitView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Views/TrainView.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Windows/CreateSplitWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml

[E:/AvaloniaApplication9.9-version1/AvaloniaApplication2/Windows/ImagePreviewWindow.axaml]
build_metadata.AdditionalFiles.SourceItemGroup = AvaloniaXaml
